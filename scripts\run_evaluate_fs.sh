#!/bin/bash

# Script to run evaluation for both train and val splits sequentially
# Usage: ./run_evaluate_fs.sh [additional_args]
# Example: ./run_evaluate_fs.sh instance_mode=True

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"

# Define splits to evaluate
SPLITS=("train" "val")

echo "Starting sequential evaluation of ${#SPLITS[@]} splits..."
echo "Project root: $PROJECT_ROOT"

# Parse additional arguments
ADDITIONAL_ARGS="$@"
if [[ -n "$ADDITIONAL_ARGS" ]]; then
    echo "Additional arguments: $ADDITIONAL_ARGS"
fi

# Run evaluation for each split
for i in "${!SPLITS[@]}"; do
    split="${SPLITS[$i]}"
    split_num=$((i + 1))
    
    echo ""
    echo "=== Split $split_num/${#SPLITS[@]}: $split ==="
    echo "Starting at: $(date)"
    
    # Run the evaluation with split override
    if python predict/evaluate.py +task=evaluate_fs path_info.split="$split" $ADDITIONAL_ARGS; then
        echo "✅ Evaluation for $split completed successfully at: $(date)"
    else
        echo "❌ Evaluation for $split failed at: $(date)"
        echo "Do you want to continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "Stopping execution"
            exit 1
        fi
    fi
done

echo ""
echo "🎉 All evaluations completed!"
echo "Finished at: $(date)"
echo ""
echo "Results saved as:"
for split in "${SPLITS[@]}"; do
    echo "  - metrics_${split}.json (or instance_metrics_${split}.json for instance mode)"
done
